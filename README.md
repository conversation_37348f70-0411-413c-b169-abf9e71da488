# Instagram Reels Scraper

A Python script that downloads all Instagram reels from a specified account using the RapidAPI Instagram Scraper API.

## Features

- Downloads all reels from a specified Instagram account
- Configurable download settings
- Customizable filename patterns
- Respects API rate limits
- Skips non-video content automatically
- Creates organized download folders

## Files

- `instagram_reels_scraper.py` - Main scraper script
- `config.py` - Configuration file
- `test_scraper.py` - Test script with mock data
- `test_api.py` - API testing script

## Setup

1. **Install Python 3** (if not already installed)

2. **Configure the scraper** by editing `config.py`:
   ```python
   # Instagram account to scrape (without @ symbol)
   INSTAGRAM_ACCOUNT = "your_target_account"
   
   # API Configuration
   RAPIDAPI_KEY = "your_rapidapi_key_here"
   
   # Download settings
   DOWNLOAD_FOLDER = "downloaded_reels"
   MAX_REELS = 50  # Set to None for unlimited
   ```

3. **Get a RapidAPI key**:
   - Go to [RapidAPI Instagram Scraper](https://rapidapi.com/hub)
   - Search for "Instagram Scraper Stable API"
   - Subscribe to the API
   - Copy your API key to `config.py`

## Usage

### Run the main scraper:
```bash
python3 instagram_reels_scraper.py
```

### Test with mock data:
```bash
python3 test_scraper.py
```

### Test API connection:
```bash
python3 test_api.py
```

## Configuration Options

### `config.py` settings:

- **INSTAGRAM_ACCOUNT**: Target Instagram username (without @)
- **RAPIDAPI_KEY**: Your RapidAPI key
- **DOWNLOAD_FOLDER**: Folder to save downloaded reels
- **MAX_REELS**: Maximum number of reels to download (None = unlimited)
- **FILENAME_PATTERN**: Pattern for naming downloaded files

### Filename Pattern Variables:
- `{username}` - Instagram account name
- `{media_id}` - Unique media ID
- `{timestamp}` - Current timestamp
- `{index}` - Sequential number

Example: `"{username}_{media_id}_{index}.mp4"` → `"account_12345_1.mp4"`

## API Response Structure

The scraper expects the Instagram API to return:

### User Media Response:
```json
{
  "data": [
    {
      "code": "media_shortcode",
      "media_type": "video"
    }
  ]
}
```

### Media Data Response:
```json
{
  "id": "media_id",
  "media_type": "video",
  "is_video": true,
  "product_type": "clips",
  "video_url": "https://video-url.mp4"
}
```

## Error Handling

The scraper handles:
- API connection errors
- Invalid media codes
- Missing video URLs
- Download failures
- Rate limiting (1 second delay between requests)

## Limitations

- Requires active RapidAPI subscription
- Subject to Instagram's rate limits
- Only downloads public content
- Depends on third-party API availability

## Troubleshooting

1. **"You are not subscribed to this API"**
   - Check your RapidAPI subscription status
   - Verify your API key is correct

2. **No reels found**
   - Verify the account name is correct
   - Check if the account has public reels
   - Try with a different account

3. **Download failures**
   - Check internet connection
   - Verify download folder permissions
   - Check available disk space

## Legal Notice

This tool is for educational purposes only. Always respect:
- Instagram's Terms of Service
- Copyright laws
- Privacy rights
- Rate limits and API terms

Use responsibly and only download content you have permission to access.
