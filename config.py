# Configuration file for Instagram Reels Scraper

# Instagram account to scrape (without @ symbol)
INSTAGRAM_ACCOUNT = "example_account"

# API Configuration
RAPIDAPI_KEY = "**************************************************"
RAPIDAPI_HOST = "instagram-scraper-stable-api.p.rapidapi.com"

# Download settings
DOWNLOAD_FOLDER = "downloaded_reels"
MAX_REELS = 50  # Maximum number of reels to download (set to None for all)

# File naming pattern
# Available variables: {username}, {media_id}, {timestamp}, {index}
FILENAME_PATTERN = "{username}_{media_id}_{index}.mp4"
