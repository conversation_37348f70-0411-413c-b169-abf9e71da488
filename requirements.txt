# Instagram Reels Scraper Requirements
# This script uses only Python standard library modules
# No additional packages required

# Standard library modules used:
# - http.client (for API requests)
# - json (for JSON parsing)
# - os (for file operations)
# - urllib.request (for downloading files)
# - urllib.parse (for URL encoding)
# - time (for delays)
# - datetime (for timestamps)

# If you want to add additional features, consider:
# requests>=2.28.0  # Alternative to http.client
# tqdm>=4.64.0      # Progress bars for downloads
