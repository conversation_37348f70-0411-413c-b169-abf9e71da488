#!/usr/bin/env python3
"""
Instagram Reels Scraper
Downloads all reels from a specified Instagram account using RapidAPI
"""

import http.client
import json
import os
import urllib.request
import urllib.parse
import time
from datetime import datetime
import config

class InstagramReelsScraper:
    def __init__(self):
        self.api_key = config.RAPIDAPI_KEY
        self.api_host = config.RAPIDAPI_HOST
        self.account = config.INSTAGRAM_ACCOUNT
        self.download_folder = config.DOWNLOAD_FOLDER
        self.max_reels = config.MAX_REELS
        self.filename_pattern = config.FILENAME_PATTERN
        
        # Create download folder if it doesn't exist
        if not os.path.exists(self.download_folder):
            os.makedirs(self.download_folder)
    
    def get_user_media(self, username):
        """Get media posts from a user's profile"""
        conn = http.client.HTTPSConnection(self.api_host)
        
        headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.api_host
        }
        
        # Encode username for URL
        encoded_username = urllib.parse.quote(username)
        endpoint = f"/get_user_media.php?username={encoded_username}"
        
        try:
            conn.request("GET", endpoint, headers=headers)
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                return json.loads(data.decode("utf-8"))
            else:
                print(f"Error getting user media: {res.status}")
                print(data.decode("utf-8"))
                return None
                
        except Exception as e:
            print(f"Error connecting to API: {e}")
            return None
        finally:
            conn.close()
    
    def get_media_data(self, media_code):
        """Get detailed media data including video URL"""
        conn = http.client.HTTPSConnection(self.api_host)
        
        headers = {
            'x-rapidapi-key': self.api_key,
            'x-rapidapi-host': self.api_host
        }
        
        endpoint = f"/get_media_data_v2.php?media_code={media_code}"
        
        try:
            conn.request("GET", endpoint, headers=headers)
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                return json.loads(data.decode("utf-8"))
            else:
                print(f"Error getting media data for {media_code}: {res.status}")
                return None
                
        except Exception as e:
            print(f"Error getting media data: {e}")
            return None
        finally:
            conn.close()
    
    def download_video(self, video_url, filename):
        """Download video from URL"""
        try:
            print(f"Downloading: {filename}")
            urllib.request.urlretrieve(video_url, filename)
            print(f"Successfully downloaded: {filename}")
            return True
        except Exception as e:
            print(f"Error downloading {filename}: {e}")
            return False
    
    def is_reel(self, media_data):
        """Check if media is a reel/video"""
        # Check various possible indicators that this is a video/reel
        if isinstance(media_data, dict):
            # Common indicators for video content
            video_indicators = [
                'video_url' in media_data,
                media_data.get('media_type') == 'video',
                media_data.get('is_video', False),
                'video_versions' in media_data,
                media_data.get('product_type') == 'clips'  # Instagram Reels
            ]
            return any(video_indicators)
        return False
    
    def extract_video_url(self, media_data):
        """Extract video URL from media data"""
        if not isinstance(media_data, dict):
            return None
            
        # Try different possible video URL fields
        video_url_fields = [
            'video_url',
            'video_download_url',
            'video_versions',
            'video_dash_manifest'
        ]
        
        for field in video_url_fields:
            if field in media_data:
                if field == 'video_versions' and isinstance(media_data[field], list):
                    # Get the first/highest quality version
                    if media_data[field]:
                        return media_data[field][0].get('url')
                else:
                    return media_data[field]
        
        return None
    
    def generate_filename(self, media_data, index):
        """Generate filename based on pattern"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        media_id = media_data.get('id', 'unknown')
        
        filename = self.filename_pattern.format(
            username=self.account,
            media_id=media_id,
            timestamp=timestamp,
            index=index
        )
        
        return os.path.join(self.download_folder, filename)
    
    def scrape_reels(self):
        """Main method to scrape all reels from the account"""
        print(f"Starting to scrape reels from @{self.account}")
        
        # Get user media
        user_media = self.get_user_media(self.account)
        if not user_media:
            print("Failed to get user media")
            return
        
        print(f"Found user media data")
        
        # Extract media codes/IDs from user media response
        media_items = []
        if isinstance(user_media, dict):
            # Try different possible structures
            if 'data' in user_media:
                media_items = user_media['data']
            elif 'items' in user_media:
                media_items = user_media['items']
            elif 'media' in user_media:
                media_items = user_media['media']
            else:
                # If it's a list directly
                if isinstance(user_media, list):
                    media_items = user_media
        
        if not media_items:
            print("No media items found")
            return
        
        print(f"Found {len(media_items)} media items")
        
        downloaded_count = 0
        reel_index = 1
        
        for item in media_items:
            if self.max_reels and downloaded_count >= self.max_reels:
                break
                
            # Extract media code
            media_code = None
            if isinstance(item, dict):
                media_code = item.get('code') or item.get('shortcode') or item.get('media_code')
            
            if not media_code:
                continue
            
            print(f"Processing media: {media_code}")
            
            # Get detailed media data
            media_data = self.get_media_data(media_code)
            if not media_data:
                continue
            
            # Check if it's a reel/video
            if not self.is_reel(media_data):
                print(f"Skipping non-video media: {media_code}")
                continue
            
            # Extract video URL
            video_url = self.extract_video_url(media_data)
            if not video_url:
                print(f"No video URL found for: {media_code}")
                continue
            
            # Generate filename and download
            filename = self.generate_filename(media_data, reel_index)
            if self.download_video(video_url, filename):
                downloaded_count += 1
                reel_index += 1
            
            # Be respectful to the API
            time.sleep(1)
        
        print(f"Scraping completed! Downloaded {downloaded_count} reels")

def main():
    scraper = InstagramReelsScraper()
    scraper.scrape_reels()

if __name__ == "__main__":
    main()
